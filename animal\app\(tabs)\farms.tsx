import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuthStore } from '@/store/auth-store';
import { useFarmStore } from '@/store/farm-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Farm, FarmStatus } from '@/types/farm';
import { Building2, Plus, MapPin } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { useThemeColors } from '@/hooks/useThemeColors';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import GenericDropdown from '@/components/GenericDropdown';
import FarmCard from '@/components/FarmCard';
import { getStaffCountsForFarms } from '@/services/staff-service';

export default function FarmsScreen() {
  const { t, language } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const { farms, fetchFarms, isLoading } = useFarmStore();
  const themedColors = useThemeColors();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFarmId, setSelectedFarmId] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [staffCounts, setStaffCounts] = useState<Record<string, number>>({});
  const styles = getStyles(themedColors, language);

  // Immediate check for valid roles - allow owner, admin, and caretaker
  if (!user || !(user.role === 'owner' || user.role === 'admin' || user.role === 'caretaker')) {
    // Use setTimeout to ensure this runs after component mount
    setTimeout(() => {
      router.replace('/(tabs)');
      Alert.alert(t('common.permissionDenied'), t('common.noAccessToThisFeature'));
    }, 0);
    // Return empty component while redirecting
    return null;
  }

  useEffect(() => {
    if (user) {
      fetchFarms(user.id);
    }
  }, [user]);

  // Fetch staff counts when farms change
  useEffect(() => {
    const fetchStaffCounts = async () => {
      if (farms.length > 0) {
        try {
          const farmIds = farms.map(farm => farm.id);
          const counts = await getStaffCountsForFarms(farmIds);
          setStaffCounts(counts);
        } catch (error) {
          console.error('Error fetching staff counts:', error);
          setStaffCounts({});
        }
      }
    };

    fetchStaffCounts();
  }, [farms]);

  // Set default selectedFarmId based on available farms
  useEffect(() => {
    if (farms.length > 0 && selectedFarmId === null) {
      setSelectedFarmId('all');
    } else if (farms.length === 0) {
      setSelectedFarmId(null);
    }
  }, [farms, selectedFarmId]);

  // Refresh farms when the screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const checkAndRefreshFarms = async () => {
        try {
          // Check if we need to refresh after session restoration
          const sessionRestored = await AsyncStorage.getItem('sessionRestored');

          if (sessionRestored === 'true' && user) {
            await fetchFarms(user.id);
            // Clear the flag after refreshing
            await AsyncStorage.removeItem('sessionRestored');
          } else if (user) {
            // Always refresh farms when screen comes into focus
            await fetchFarms(user.id);
          } else {
            console.warn('Cannot refresh farms - no user is logged in');
          }
        } catch (error) {
          console.error('Error refreshing farms on focus:', error);
        }
      };

      checkAndRefreshFarms();

      return () => { };
    }, [])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (user) {
        await fetchFarms(user.id);
        // Refetch staff counts after farms are refreshed
        const farmIds = farms.map(farm => farm.id);
        if (farmIds.length > 0) {
          const counts = await getStaffCountsForFarms(farmIds);
          setStaffCounts(counts);
        }
      }
    } catch (error) {
      console.error('Error refreshing farms:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleAddFarm = () => {
    router.push('/farms/add');
  };

  const handleFarmPress = (farmId: string) => {
    try {
      console.log('FarmsScreen: handleFarmPress called with farmId:', farmId);
      console.log('FarmsScreen: Available farms:', farms.map(f => ({ id: f.id, name: f.name })));

      if (!farmId || farmId === 'undefined' || farmId === 'null') {
        console.error('FarmsScreen: Invalid farm ID', farmId);
        Alert.alert(t('common.error'), t('common.invalidFarmId') || 'Invalid farm ID');
        return;
      }

      // Check if farm exists in the store
      const farm = farms.find(f => f.id === farmId);
      if (!farm) {
        console.error('FarmsScreen: Farm not found', farmId);
        Alert.alert(t('common.error'), t('common.farmNotFound') || 'Farm not found');
        return;
      }

      console.log('FarmsScreen: Navigating to farm detail:', farmId);
      router.push(`/farms/${farmId}`);
      console.log('FarmsScreen: Navigation call completed');
    } catch (error) {
      console.error('FarmsScreen: Navigation error', error);
      Alert.alert(t('common.error'), t('common.navigationError') || 'Navigation failed');
    }
  };

  const handleStatusFilter = (status: string | null) => {
    setSelectedStatus(status);
  };

  // Create farm dropdown items - only if user has farms
  const farmDropdownItems = useMemo(() => {
    if (farms.length === 0) return [];

    return farms.map(farm => ({
      id: farm.id,
      label: farm.name,
      description: typeof farm.location === 'string' ? farm.location : farm.location?.address || 'No location',
      icon: <MapPin size={20} color={themedColors.primary} />
    }));
  }, [farms, themedColors.primary]);

  // Filter farms based on status only (show all farms by default, filter only when explicitly selected)
  const filteredFarms = farms.filter(farm => {
    // Only filter by farm if a specific farm is selected in the dropdown (not by dashboard selection)
    const matchesFarm = !selectedFarmId || farm.id === selectedFarmId;
    const matchesStatus = selectedStatus ? farm.status === selectedStatus : true;
    return matchesFarm && matchesStatus;
  });

  if (isLoading && !refreshing) {
    <SafeAreaView style={[styles.container, { backgroundColor: themedColors?.background }]} edges={['left', 'right']}>
      <LoadingIndicator fullScreen message={t('farms.loading')} />
    </SafeAreaView>
    // return <LoadingIndicator fullScreen message={t('farms.loading')} />;
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom', 'left', 'right']}>
      {/* Header removed as requested */}

      {/* Only show farm dropdown if user has farms */}
      {farms.length > 0 && (
        <View style={styles.searchContainer}>
          <GenericDropdown
            placeholder={t('farms.selectFarmPlaceholder')}
            items={farmDropdownItems}
            value={selectedFarmId || ''}
            onSelect={setSelectedFarmId}
            modalTitle={t('farms.selectFarm')}
            searchPlaceholder={t('farms.searchFarms')}
            containerStyle={styles.dropdownContainer}
          />
        </View>
      )}

      {/* Floating Add Button - Only show for owners and admins */}
      {(user?.role === 'owner' || user?.role === 'admin') && (
        <TouchableOpacity
          style={styles.floatingAddButton}
          onPress={handleAddFarm}
        >
          <Plus size={24} color="white" />
        </TouchableOpacity>
      )}

      <View style={styles.filterContainer}>
        {/* Filter label removed as requested */}
        <View style={[
          styles.filterButtons,
          language === 'ur' && styles.urduFilterButtons
        ]}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedStatus === null && styles.filterButtonSelected,
              language === 'ur' && styles.urduFilterButton
            ]}
            onPress={() => handleStatusFilter(null)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedStatus === null && styles.filterButtonTextSelected,
              language === 'ur' ? styles.urduText : null
            ]}>
              {t('common.all')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedStatus === FarmStatus.ACTIVE && styles.filterButtonSelected,
              language === 'ur' && styles.urduFilterButton
            ]}
            onPress={() => handleStatusFilter(FarmStatus.ACTIVE)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedStatus === FarmStatus.ACTIVE && styles.filterButtonTextSelected,
              language === 'ur' ? styles.urduText : null
            ]}>
              {t('farms.statusActive')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedStatus === FarmStatus.INACTIVE && styles.filterButtonSelected,
              language === 'ur' && styles.urduFilterButton
            ]}
            onPress={() => handleStatusFilter(FarmStatus.INACTIVE)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedStatus === FarmStatus.INACTIVE && styles.filterButtonTextSelected,
              language === 'ur' ? styles.urduText : null
            ]}>
              {t('farms.statusInactive')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {filteredFarms.length === 0 ? (
        <EmptyState
          title={t('farms.noFarms')}
          message={selectedFarmId && selectedFarmId !== 'all' || selectedStatus
            ? t('common.tryDifferentSearch')
            : t('farms.addYourFirstFarm')
          }
          actionLabel={selectedFarmId && selectedFarmId !== 'all' || selectedStatus ? undefined : t('farms.addFarm')} // @ts-ignore
          onAction={selectedFarmId && selectedFarmId !== 'all' || selectedStatus ? undefined : handleAddFarm}
          icon={<Building2 size={48} color={themedColors.primary} />}
          style={styles.emptyState}
        />
      ) : (
        <FlatList
          data={filteredFarms}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <FarmCard
              farm={item}
              onPress={handleFarmPress}
              staffCount={staffCounts[item.id] || 0}
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
        />
      )}
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>, language: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: themedColors.text,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12, // Apply padding to top and bottom for internal spacing
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // backgroundColor: themedColors.card, // Use themed card color
    zIndex: 2,
    minHeight: 75
  },
  dropdownContainer: {
    flex: 1,
    // backgroundColor: themedColors.inputBackground || themedColors.card, // Ensure dropdown itself is themed
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingTop: 9,
    marginTop: 5, // Add some margin since we removed the filter label
    // backgroundColor: themedColors.card, // Use themed card color
    zIndex: 1,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: themedColors.text,
    marginBottom: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  urduFilterButtons: {
    flexDirection: 'row-reverse',
  },
  filterButton: {
    backgroundColor: themedColors.background, // Use a slightly different background for unselected buttons
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
  },
  urduFilterButton: {
    marginRight: 0,
    marginLeft: 8,
  },
  filterButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  filterButtonText: {
    color: themedColors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  filterButtonTextSelected: {
    color: 'white',
  },
  listContent: {
    padding: 16,
  },
  // Farm card specific styles removed as they're now in the FarmCard component
  emptyState: {
    marginTop: 40,
  },
  urduText: {
    fontFamily: 'System',
    textAlign: 'right',
  },
  floatingAddButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: themedColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: themedColors.isDarkMode ? '#000' : '#000', // Use themed shadow color
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: themedColors.isDarkMode ? 0.3 : 0.25,
    shadowRadius: 3.84,
    zIndex: 10,
  },
});
