[debug] [2025-07-29T07:54:05.178Z] ----------------------------------------------------------------------
[debug] [2025-07-29T07:54:05.183Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-29T07:54:05.183Z] CLI Version:   14.8.0
[debug] [2025-07-29T07:54:05.183Z] Platform:      win32
[debug] [2025-07-29T07:54:05.184Z] Node Version:  v22.14.0
[debug] [2025-07-29T07:54:05.184Z] Time:          Tue Jul 29 2025 19:54:05 GMT+1200 (New Zealand Standard Time)
[debug] [2025-07-29T07:54:05.184Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-29T07:54:05.523Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-29T07:54:05.524Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-29T07:54:05.536Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T07:54:05.536Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-29T07:54:05.543Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-kissandost-9570f.json
[debug] [2025-07-29T07:54:05.570Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T07:54:05.570Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T07:54:05.570Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-29T07:54:05.571Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-29T07:54:05.588Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json
[debug] [2025-07-29T07:54:05.591Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\malikeahtesham111_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\malikeahtesham111_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-29T07:54:05.592Z] Checked if tokens are valid: true, expires at: 1753776549415
[debug] [2025-07-29T07:54:05.592Z] Checked if tokens are valid: true, expires at: 1753776549415
[debug] [2025-07-29T07:54:05.593Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig [none]
[debug] [2025-07-29T07:54:06.435Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig 200
[debug] [2025-07-29T07:54:06.436Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/kissandost-9570f/adminSdkConfig {"projectId":"kissandost-9570f","databaseURL":"https://kissandost-9570f-default-rtdb.firebaseio.com","storageBucket":"kissandost-9570f.firebasestorage.app"}
[info] i  functions: Watching "E:\python_projects_factory\animal\mcp-server\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"E:\\python_projects_factory\\animal\\mcp-server\\functions\" for Cloud Functions..."}}
[debug] [2025-07-29T07:54:06.474Z] Validating nodejs source
[debug] [2025-07-29T07:54:08.018Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.1",
    "express": "^4.18.2",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "multer": "^1.4.5-lts.1",
    "openai": "^4.20.1",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-07-29T07:54:08.018Z] Building nodejs source
[debug] [2025-07-29T07:54:08.019Z] Failed to find version of module node: reached end of search path E:\python_projects_factory\animal\mcp-server\functions\node_modules
[info] +  functions: Using node@22 from host. 
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-29T07:54:08.023Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-29T07:54:08.031Z] Found firebase-functions binary at 'E:\python_projects_factory\animal\mcp-server\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8713

[info] [dotenv@17.2.1] injecting env (2) from .env -- tip: ⚙️  suppress all logs with { quiet: true }

[debug] [2025-07-29T07:54:08.658Z] Got response from /__/functions.yaml {"endpoints":{"animalApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","region":["australia-southeast1"],"labels":{},"httpsTrigger":{},"entryPoint":"animalApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] +  functions: Loaded functions definitions from source: animalApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: animalApp."}}
[info] +  functions[australia-southeast1-animalApp]: http function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/kissandost-9570f/australia-southeast1/animalApp)."}}
[debug] [2025-07-29T07:54:12.712Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-29T07:54:30.110Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:30.109Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:30.110Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:30.109Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:30.110Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:30.115Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:30.115Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[info] i  functions: Loaded environment variables from .env. 
[debug] [2025-07-29T07:54:30.126Z] [worker-pool] addWorker(australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] addWorker(australia-southeast1-animalApp)"}}
[debug] [2025-07-29T07:54:30.128Z] [worker-pool] Adding worker with key australia-southeast1-animalApp, total=1 {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] Adding worker with key australia-southeast1-animalApp, total=1"}}
[debug] [2025-07-29T07:54:31.882Z] [runtime-status] [23316] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-29T07:54:31.883Z] [runtime-status] [23316] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T07:54:31.884Z] [runtime-status] [23316] Outgoing network have been stubbed. [{"name":"http","status":"mocked"},{"name":"http","status":"mocked"},{"name":"https","status":"mocked"},{"name":"https","status":"mocked"},{"name":"net","status":"mocked"}] {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Outgoing network have been stubbed. [{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"http\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"https\",\"status\":\"mocked\"},{\"name\":\"net\",\"status\":\"mocked\"}]"}}
[debug] [2025-07-29T07:54:31.885Z] [runtime-status] [23316] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T07:54:32.359Z] [runtime-status] [23316] Checked functions.config() {"config":{}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Checked functions.config() {\"config\":{}}"}}
[debug] [2025-07-29T07:54:32.359Z] [runtime-status] [23316] firebase-functions has been stubbed. {"functionsResolution":{"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] firebase-functions has been stubbed. {\"functionsResolution\":{\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}}"}}
[debug] [2025-07-29T07:54:32.360Z] [runtime-status] [23316] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T07:54:32.367Z] [runtime-status] [23316] Resolved module firebase-admin {"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Resolved module firebase-admin {\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}"}}
[debug] [2025-07-29T07:54:32.370Z] [runtime-status] [23316] Resolved module firebase-functions {"declared":true,"installed":true,"version":"6.4.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-functions\\lib\\v2\\index.js"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Resolved module firebase-functions {\"declared\":true,\"installed\":true,\"version\":\"6.4.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-functions\\\\lib\\\\v2\\\\index.js\"}"}}
[debug] [2025-07-29T07:54:32.371Z] [runtime-status] [23316] firebase-admin has been stubbed. {"adminResolution":{"declared":true,"installed":true,"version":"12.7.0","resolution":"E:\\python_projects_factory\\animal\\mcp-server\\functions\\node_modules\\firebase-admin\\lib\\index.js"}} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] firebase-admin has been stubbed. {\"adminResolution\":{\"declared\":true,\"installed\":true,\"version\":\"12.7.0\",\"resolution\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\\\\node_modules\\\\firebase-admin\\\\lib\\\\index.js\"}}"}}
[info] >  [dotenv@17.2.1] injecting env (0) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar {"user":"[dotenv@17.2.1] injecting env (0) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m [dotenv@17.2.1] injecting env (0) from .env -- tip: 📡 observe env with Radar: https://dotenvx.com/radar"}}
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
[debug] [2025-07-29T07:54:32.581Z] [runtime-status] [23316] Functions runtime initialized. {"cwd":"E:\\python_projects_factory\\animal\\mcp-server\\functions","node_version":"22.14.0"} {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Functions runtime initialized. {\"cwd\":\"E:\\\\python_projects_factory\\\\animal\\\\mcp-server\\\\functions\",\"node_version\":\"22.14.0\"}"}}
[debug] [2025-07-29T07:54:32.581Z] [runtime-status] [23316] Listening to port: \\?\pipe\fire_emu_8c7ef5407375f3f4 {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[runtime-status] [23316] Listening to port: \\\\?\\pipe\\fire_emu_8c7ef5407375f3f4"}}
[debug] [2025-07-29T07:54:32.617Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:32.617Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:32.618Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:54:32.623Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 5.7489ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 5.7489ms"}}
[debug] [2025-07-29T07:54:32.624Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:32.624Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:32.625Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:32.625Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:32.627Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:32.627Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:32.627Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:32.627Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:32.627Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:32.628Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:32.628Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:32.628Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:32.628Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  🔁 Initializing routes... {"user":"🔁 Initializing routes...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Initializing routes..."}}
[info] >  OpenAI client initialized successfully {"user":"OpenAI client initialized successfully","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m OpenAI client initialized successfully"}}
[warn] !  functions: The Cloud Firestore emulator is not running, so calls to Firestore will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Cloud Firestore emulator is not running, so calls to Firestore will affect production."}}
[warn] !  functions: The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"The Firebase Storage emulator is not running, so calls to Firebase Storage will affect production."}}
[info] >  🔁 Routes Initialized Successfully... {"user":"🔁 Routes Initialized Successfully...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔁 Routes Initialized Successfully..."}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-29T07:54:33.939Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 1310.975ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 1310.975ms"}}
[debug] [2025-07-29T07:54:33.940Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:33.940Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:33.940Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:33.940Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Edit', {"user":"  prompt: 'Edit',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Edit',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ✏️ GENERAL EDIT PATTERN MATCHED! {"user":"✏️ GENERAL EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ GENERAL EDIT PATTERN MATCHED!"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Edit', {"user":"  prompt: 'Edit',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Edit',"}}
[info] >    requestType: 'edit_general', {"user":"  requestType: 'edit_general',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'edit_general',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  ✏️ GENERAL EDIT HANDLER TRIGGERED - Processing general edit request - showing module selection... {"user":"✏️ GENERAL EDIT HANDLER TRIGGERED - Processing general edit request - showing module selection...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ GENERAL EDIT HANDLER TRIGGERED - Processing general edit request - showing module selection..."}}
[info] >  📋 Sending edit modules response: { {"user":"📋 Sending edit modules response: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📋 Sending edit modules response: {"}}
[info] >    message: '✏️ Edit\n' + {"user":"  message: '✏️ Edit\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   message: '✏️ Edit\\n' +"}}
[info] >      '\n' + {"user":"    '\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '\\n' +"}}
[info] >      'What would you like to edit? Select from below:\n' + {"user":"    'What would you like to edit? Select from below:\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'What would you like to edit? Select from below:\\n' +"}}
[info] >      '\n' + {"user":"    '\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '\\n' +"}}
[info] >      '🏡 **Farm** - Edit farm details\n' + {"user":"    '🏡 **Farm** - Edit farm details\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '🏡 **Farm** - Edit farm details\\n' +"}}
[info] >      '🐄 **Animal** - Edit animal details\n' + {"user":"    '🐄 **Animal** - Edit animal details\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '🐄 **Animal** - Edit animal details\\n' +"}}
[info] >      '💰 **Expense** - Edit expense details\n' + {"user":"    '💰 **Expense** - Edit expense details\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '💰 **Expense** - Edit expense details\\n' +"}}
[info] >      '🏥 **Health Record** - Edit health records\n' + {"user":"    '🏥 **Health Record** - Edit health records\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '🏥 **Health Record** - Edit health records\\n' +"}}
[info] >      '📋 **Task** - Edit task details\n' + {"user":"    '📋 **Task** - Edit task details\\n' +","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '📋 **Task** - Edit task details\\n' +"}}
[info] >      '👤 **Employee** - Edit employee details', {"user":"    '👤 **Employee** - Edit employee details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '👤 **Employee** - Edit employee details',"}}
[info] >    editModulesCount: 6, {"user":"  editModulesCount: 6,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   editModulesCount: 6,"}}
[info] >    editModules: [ {"user":"  editModules: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   editModules: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'farm', {"user":"      id: 'farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'farm',"}}
[info] >        name: 'Farm', {"user":"      name: 'Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm',"}}
[info] >        description: 'Edit farm details', {"user":"      description: 'Edit farm details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit farm details',"}}
[info] >        icon: '🏡' {"user":"      icon: '🏡'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '🏡'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'animal', {"user":"      id: 'animal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'animal',"}}
[info] >        name: 'Animal', {"user":"      name: 'Animal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Animal',"}}
[info] >        description: 'Edit animal details', {"user":"      description: 'Edit animal details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit animal details',"}}
[info] >        icon: '🐄' {"user":"      icon: '🐄'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '🐄'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'expense', {"user":"      id: 'expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'expense',"}}
[info] >        name: 'Expense', {"user":"      name: 'Expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Expense',"}}
[info] >        description: 'Edit expense details', {"user":"      description: 'Edit expense details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit expense details',"}}
[info] >        icon: '💰' {"user":"      icon: '💰'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '💰'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'health_record', {"user":"      id: 'health_record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'health_record',"}}
[info] >        name: 'Health Record', {"user":"      name: 'Health Record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Health Record',"}}
[info] >        description: 'Edit health records', {"user":"      description: 'Edit health records',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit health records',"}}
[info] >        icon: '🏥' {"user":"      icon: '🏥'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '🏥'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'task', {"user":"      id: 'task',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'task',"}}
[info] >        name: 'Task', {"user":"      name: 'Task',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Task',"}}
[info] >        description: 'Edit task details', {"user":"      description: 'Edit task details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit task details',"}}
[info] >        icon: '📋' {"user":"      icon: '📋'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '📋'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'employee', {"user":"      id: 'employee',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'employee',"}}
[info] >        name: 'Employee', {"user":"      name: 'Employee',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Employee',"}}
[info] >        description: 'Edit employee details', {"user":"      description: 'Edit employee details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit employee details',"}}
[info] >        icon: '👤' {"user":"      icon: '👤'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '👤'"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    moduleImages: [ {"user":"  moduleImages: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   moduleImages: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        imageUri: null, {"user":"      imageUri: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       imageUri: null,"}}
[info] >        name: 'Farm', {"user":"      name: 'Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm',"}}
[info] >        description: 'Edit farm details', {"user":"      description: 'Edit farm details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit farm details',"}}
[info] >        id: 'farm', {"user":"      id: 'farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'farm',"}}
[info] >        icon: '🏡' {"user":"      icon: '🏡'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '🏡'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        imageUri: null, {"user":"      imageUri: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       imageUri: null,"}}
[info] >        name: 'Animal', {"user":"      name: 'Animal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Animal',"}}
[info] >        description: 'Edit animal details', {"user":"      description: 'Edit animal details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit animal details',"}}
[info] >        id: 'animal', {"user":"      id: 'animal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'animal',"}}
[info] >        icon: '🐄' {"user":"      icon: '🐄'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '🐄'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        imageUri: null, {"user":"      imageUri: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       imageUri: null,"}}
[info] >        name: 'Expense', {"user":"      name: 'Expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Expense',"}}
[info] >        description: 'Edit expense details', {"user":"      description: 'Edit expense details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit expense details',"}}
[info] >        id: 'expense', {"user":"      id: 'expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'expense',"}}
[info] >        icon: '💰' {"user":"      icon: '💰'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '💰'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        imageUri: null, {"user":"      imageUri: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       imageUri: null,"}}
[info] >        name: 'Health Record', {"user":"      name: 'Health Record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Health Record',"}}
[info] >        description: 'Edit health records', {"user":"      description: 'Edit health records',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit health records',"}}
[info] >        id: 'health_record', {"user":"      id: 'health_record',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'health_record',"}}
[info] >        icon: '🏥' {"user":"      icon: '🏥'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '🏥'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        imageUri: null, {"user":"      imageUri: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       imageUri: null,"}}
[info] >        name: 'Task', {"user":"      name: 'Task',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Task',"}}
[info] >        description: 'Edit task details', {"user":"      description: 'Edit task details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit task details',"}}
[info] >        id: 'task', {"user":"      id: 'task',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'task',"}}
[info] >        icon: '📋' {"user":"      icon: '📋'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '📋'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        imageUri: null, {"user":"      imageUri: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       imageUri: null,"}}
[info] >        name: 'Employee', {"user":"      name: 'Employee',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Employee',"}}
[info] >        description: 'Edit employee details', {"user":"      description: 'Edit employee details',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       description: 'Edit employee details',"}}
[info] >        id: 'employee', {"user":"      id: 'employee',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'employee',"}}
[info] >        icon: '👤' {"user":"      icon: '👤'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       icon: '👤'"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    needsModuleSelection: true, {"user":"  needsModuleSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   needsModuleSelection: true,"}}
[info] >    selectionType: 'edit_module' {"user":"  selectionType: 'edit_module'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectionType: 'edit_module'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-29T07:54:37.995Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:37.995Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:37.995Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:37.995Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:37.995Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:37.998Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:37.998Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:37.998Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:37.999Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:54:38.001Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.8965ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.8965ms"}}
[debug] [2025-07-29T07:54:38.007Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:38.007Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:38.008Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:38.008Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:38.009Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:38.009Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:38.009Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:38.009Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:38.009Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:38.012Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:38.012Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:38.013Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:38.014Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  📨 Context action: select_edit_module {"user":"📨 Context action: select_edit_module","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: select_edit_module"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    selectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  selectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    currentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  currentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   currentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven_View', {"user":"      name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven_View',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { action: 'select_edit_module', needsModuleSelection: true }, {"user":"  context: { action: 'select_edit_module', needsModuleSelection: true },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: { action: 'select_edit_module', needsModuleSelection: true },"}}
[info] >    prompt: 'Expense' {"user":"  prompt: 'Expense'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Expense'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Expense', {"user":"  prompt: 'Expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Expense',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextKeys: [ 'action', 'needsModuleSelection' ], {"user":"  contextKeys: [ 'action', 'needsModuleSelection' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [ 'action', 'needsModuleSelection' ],"}}
[info] >    contextAction: 'select_edit_module', {"user":"  contextAction: 'select_edit_module',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: 'select_edit_module',"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Expense', {"user":"  prompt: 'Expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Expense',"}}
[info] >    requestType: 'expense', {"user":"  requestType: 'expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'expense',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextAction: 'select_edit_module' {"user":"  contextAction: 'select_edit_module'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: 'select_edit_module'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: { {"user":"📨 Context received: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: {"}}
[info] >    "action": "select_edit_module", {"user":"  \"action\": \"select_edit_module\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"select_edit_module\","}}
[info] >    "needsModuleSelection": true {"user":"  \"needsModuleSelection\": true","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"needsModuleSelection\": true"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context action: select_edit_module {"user":"📨 Context action: select_edit_module","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: select_edit_module"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Processing edit module selection... {"user":"Processing edit module selection...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing edit module selection..."}}
[info] >  Selected module ID for editing: expense {"user":"Selected module ID for editing: expense","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected module ID for editing: expense"}}
[info] >  Redirecting to edit_expense handler... {"user":"Redirecting to edit_expense handler...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Redirecting to edit_expense handler..."}}
[info] >  Detected current farm from request: Haven_View {"user":"Detected current farm from request: Haven_View","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Detected current farm from request: Haven_View"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    selectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  selectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    currentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  currentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   currentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven_View', {"user":"      name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven_View',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      selectedFarm: { {"user":"    selectedFarm: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     selectedFarm: {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven_View', {"user":"      name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven_View',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      currentFarmId: '7owH6eTfGSYqaRpgIsQE' {"user":"    currentFarmId: '7owH6eTfGSYqaRpgIsQE'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     currentFarmId: '7owH6eTfGSYqaRpgIsQE'"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'edit expense' {"user":"  prompt: 'edit expense'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'edit expense'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'edit expense', {"user":"  prompt: 'edit expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'edit expense',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextKeys: [ 'selectedFarm', 'currentFarmId' ], {"user":"  contextKeys: [ 'selectedFarm', 'currentFarmId' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [ 'selectedFarm', 'currentFarmId' ],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: true {"user":"  contextSelectedFarm: true","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: true"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ✏️ EXPENSE EDIT PATTERN MATCHED! {"user":"✏️ EXPENSE EDIT PATTERN MATCHED!","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ✏️ EXPENSE EDIT PATTERN MATCHED!"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'edit expense', {"user":"  prompt: 'edit expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'edit expense',"}}
[info] >    requestType: 'edit_expense', {"user":"  requestType: 'edit_expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'edit_expense',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: { {"user":"📨 Context received: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: {"}}
[info] >    "selectedFarm": { {"user":"  \"selectedFarm\": {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"selectedFarm\": {"}}
[info] >      "id": "7owH6eTfGSYqaRpgIsQE", {"user":"    \"id\": \"7owH6eTfGSYqaRpgIsQE\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"id\": \"7owH6eTfGSYqaRpgIsQE\","}}
[info] >      "name": "Haven_View", {"user":"    \"name\": \"Haven_View\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"name\": \"Haven_View\","}}
[info] >      "location": { {"user":"    \"location\": {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"location\": {"}}
[info] >        "address": "Dina, Jhelum", {"user":"      \"address\": \"Dina, Jhelum\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       \"address\": \"Dina, Jhelum\","}}
[info] >        "longitude": 0, {"user":"      \"longitude\": 0,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       \"longitude\": 0,"}}
[info] >        "latitude": 0 {"user":"      \"latitude\": 0","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       \"latitude\": 0"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    "currentFarmId": "7owH6eTfGSYqaRpgIsQE" {"user":"  \"currentFarmId\": \"7owH6eTfGSYqaRpgIsQE\"","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"currentFarmId\": \"7owH6eTfGSYqaRpgIsQE\""}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Processing expense edit request... {"user":"Processing expense edit request...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing expense edit request..."}}
[info] >  User selected expense module - showing expense list for selection {"user":"User selected expense module - showing expense list for selection","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m User selected expense module - showing expense list for selection"}}
[info] >  Current farm context for expense edit: 7owH6eTfGSYqaRpgIsQE {"user":"Current farm context for expense edit: 7owH6eTfGSYqaRpgIsQE","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Current farm context for expense edit: 7owH6eTfGSYqaRpgIsQE"}}
[info] >  Available context sources for expenses: { {"user":"Available context sources for expenses: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Available context sources for expenses: {"}}
[info] >    contextCurrentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  contextCurrentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextCurrentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    contextSelectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  contextSelectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    requestSelectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  requestSelectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestSelectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    requestCurrentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  requestCurrentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestCurrentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    farmsCount: 1 {"user":"  farmsCount: 1","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmsCount: 1"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Request body farms: [ {"user":"Request body farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Request body farms: ["}}
[info] >    { {"user":"  {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   {"}}
[info] >      id: '7owH6eTfGSYqaRpgIsQE', {"user":"    id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      name: 'Haven_View', {"user":"    name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     name: 'Haven_View',"}}
[info] >      location: { address: 'Dina, Jhelum', longitude: 0, latitude: 0 } {"user":"    location: { address: 'Dina, Jhelum', longitude: 0, latitude: 0 }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     location: { address: 'Dina, Jhelum', longitude: 0, latitude: 0 }"}}
[info] >    } {"user":"  }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   }"}}
[info] >  ] {"user":"]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ]"}}
[info] >  Filtering expenses to current farm only: 7owH6eTfGSYqaRpgIsQE {"user":"Filtering expenses to current farm only: 7owH6eTfGSYqaRpgIsQE","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Filtering expenses to current farm only: 7owH6eTfGSYqaRpgIsQE"}}
[info] >  Farms to query for expenses: [ {"user":"Farms to query for expenses: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Farms to query for expenses: ["}}
[info] >    { {"user":"  {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   {"}}
[info] >      id: '7owH6eTfGSYqaRpgIsQE', {"user":"    id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      name: 'Haven_View', {"user":"    name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     name: 'Haven_View',"}}
[info] >      location: { address: 'Dina, Jhelum', longitude: 0, latitude: 0 } {"user":"    location: { address: 'Dina, Jhelum', longitude: 0, latitude: 0 }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     location: { address: 'Dina, Jhelum', longitude: 0, latitude: 0 }"}}
[info] >    } {"user":"  }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   }"}}
[info] >  ] {"user":"]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ]"}}
[info] >  User farms resolved: [ { id: '7owH6eTfGSYqaRpgIsQE', name: 'Haven_View' } ] {"user":"User farms resolved: [ { id: '7owH6eTfGSYqaRpgIsQE', name: 'Haven_View' } ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m User farms resolved: [ { id: '7owH6eTfGSYqaRpgIsQE', name: 'Haven_View' } ]"}}
[info] >  Fetching expenses for farm: 7owH6eTfGSYqaRpgIsQE (Haven_View) {"user":"Fetching expenses for farm: 7owH6eTfGSYqaRpgIsQE (Haven_View)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Fetching expenses for farm: 7owH6eTfGSYqaRpgIsQE (Haven_View)"}}
[info] >  Found 5 expenses for farm 7owH6eTfGSYqaRpgIsQE {"user":"Found 5 expenses for farm 7owH6eTfGSYqaRpgIsQE","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Found 5 expenses for farm 7owH6eTfGSYqaRpgIsQE"}}
[info] >  Expense found: rvXmv8MzPLyyXtTr0ZdD { amount: 8716, category: 'Feed', farmId: '7owH6eTfGSYqaRpgIsQE' } {"user":"Expense found: rvXmv8MzPLyyXtTr0ZdD { amount: 8716, category: 'Feed', farmId: '7owH6eTfGSYqaRpgIsQE' }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Expense found: rvXmv8MzPLyyXtTr0ZdD { amount: 8716, category: 'Feed', farmId: '7owH6eTfGSYqaRpgIsQE' }"}}
[info] >  Expense found: GKSkF1T5b0EZMucMIujQ { amount: 3716, category: 'Feed', farmId: '7owH6eTfGSYqaRpgIsQE' } {"user":"Expense found: GKSkF1T5b0EZMucMIujQ { amount: 3716, category: 'Feed', farmId: '7owH6eTfGSYqaRpgIsQE' }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Expense found: GKSkF1T5b0EZMucMIujQ { amount: 3716, category: 'Feed', farmId: '7owH6eTfGSYqaRpgIsQE' }"}}
[info] >  Expense found: 7ZP3MNJXONTJn1GZHfkN { {"user":"Expense found: 7ZP3MNJXONTJn1GZHfkN {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Expense found: 7ZP3MNJXONTJn1GZHfkN {"}}
[info] >    amount: 1765.84, {"user":"  amount: 1765.84,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   amount: 1765.84,"}}
[info] >    category: 'Veterinary Service', {"user":"  category: 'Veterinary Service',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   category: 'Veterinary Service',"}}
[info] >    farmId: '7owH6eTfGSYqaRpgIsQE' {"user":"  farmId: '7owH6eTfGSYqaRpgIsQE'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmId: '7owH6eTfGSYqaRpgIsQE'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Expense found: EAle1DWp6DPIEk2EXs2E { {"user":"Expense found: EAle1DWp6DPIEk2EXs2E {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Expense found: EAle1DWp6DPIEk2EXs2E {"}}
[info] >    amount: 52.36, {"user":"  amount: 52.36,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   amount: 52.36,"}}
[info] >    category: 'Animal Purchase', {"user":"  category: 'Animal Purchase',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   category: 'Animal Purchase',"}}
[info] >    farmId: '7owH6eTfGSYqaRpgIsQE' {"user":"  farmId: '7owH6eTfGSYqaRpgIsQE'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmId: '7owH6eTfGSYqaRpgIsQE'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Expense found: NCYuVjrvLhPJP7obcTbx { amount: 989.38, category: 'Labor', farmId: '7owH6eTfGSYqaRpgIsQE' } {"user":"Expense found: NCYuVjrvLhPJP7obcTbx { amount: 989.38, category: 'Labor', farmId: '7owH6eTfGSYqaRpgIsQE' }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Expense found: NCYuVjrvLhPJP7obcTbx { amount: 989.38, category: 'Labor', farmId: '7owH6eTfGSYqaRpgIsQE' }"}}
[info] >  Total expenses found across all farms: 5 {"user":"Total expenses found across all farms: 5","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Total expenses found across all farms: 5"}}
[debug] [2025-07-29T07:54:41.050Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 3036.2325ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 3036.2325ms"}}
[debug] [2025-07-29T07:54:41.051Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:41.052Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:41.052Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[debug] [2025-07-29T07:54:41.052Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:41.052Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:46.179Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:46.179Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:46.180Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:46.179Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:46.180Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:46.182Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:46.182Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:46.183Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:46.183Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:54:46.186Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 3.2756ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 3.2756ms"}}
[debug] [2025-07-29T07:54:46.195Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:46.195Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:46.195Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:46.195Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:46.196Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:46.196Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:46.196Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:46.196Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:46.196Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:46.199Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:46.199Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:46.199Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:46.201Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  📨 Context action: edit_expense {"user":"📨 Context action: edit_expense","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_expense"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    prompt: 'NCYuVjrvLhPJP7obcTbx', {"user":"  prompt: 'NCYuVjrvLhPJP7obcTbx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'NCYuVjrvLhPJP7obcTbx',"}}
[info] >    selectedExpenseId: 'NCYuVjrvLhPJP7obcTbx', {"user":"  selectedExpenseId: 'NCYuVjrvLhPJP7obcTbx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedExpenseId: 'NCYuVjrvLhPJP7obcTbx',"}}
[info] >    selectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  selectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    currentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  currentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   currentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_expense', {"user":"    action: 'edit_expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_expense',"}}
[info] >      needsExpenseSelection: true, {"user":"    needsExpenseSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsExpenseSelection: true,"}}
[info] >      availableExpenses: [ [Object], [Object], [Object], [Object], [Object] ] {"user":"    availableExpenses: [ [Object], [Object], [Object], [Object], [Object] ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableExpenses: [ [Object], [Object], [Object], [Object], [Object] ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven_View', {"user":"      name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven_View',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'BtniJTurDAn0saHHbEGF', {"user":"      id: 'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'BtniJTurDAn0saHHbEGF',"}}
[info] >        name: 'Green Land..', {"user":"      name: 'Green Land..',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Green Land..',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'wFSF3LbHQwPYy6A0pu94', {"user":"      id: 'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'wFSF3LbHQwPYy6A0pu94',"}}
[info] >        name: 'Dairy House ', {"user":"      name: 'Dairy House ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Dairy House ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'fNgkVoyHni01zmozumbK', {"user":"      id: 'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'fNgkVoyHni01zmozumbK',"}}
[info] >        name: 'Octans Farm', {"user":"      name: 'Octans Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Octans Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'oXnGAP4V78vilhIVpIQN', {"user":"      id: 'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'oXnGAP4V78vilhIVpIQN',"}}
[info] >        name: 'Trade Farm', {"user":"      name: 'Trade Farm',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Trade Farm',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'NpS3CdrpHvPZClMMDeFc', {"user":"      id: 'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'NpS3CdrpHvPZClMMDeFc',"}}
[info] >        name: 'River view ', {"user":"      name: 'River view ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'River view ',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'TlS4g9PWKN1wNBtjHEoq', {"user":"      id: 'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >        name: 'Sabz Chara', {"user":"      name: 'Sabz Chara',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Sabz Chara',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'rb2n32HuXJlFOk44cAkJ', {"user":"      id: 'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'rb2n32HuXJlFOk44cAkJ',"}}
[info] >        name: 'Farm_002', {"user":"      name: 'Farm_002',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_002',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'UWkNKuXggt70Oe4bkzcx', {"user":"      id: 'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'UWkNKuXggt70Oe4bkzcx',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'JRAQxnj2u4vmHNuPHz3W', {"user":"      id: 'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'H3LThue9rQ8V1Pz8Qdal', {"user":"      id: 'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      }, {"user":"    },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     },"}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: 'JI3IBGjWKZUnJxguyPTH', {"user":"      id: 'JI3IBGjWKZUnJxguyPTH',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: 'JI3IBGjWKZUnJxguyPTH',"}}
[info] >        name: 'Farm_003', {"user":"      name: 'Farm_003',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Farm_003',"}}
[info] >        ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12' {"user":"      ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       ownerId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12'"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-29T07:54:46.519Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 318.039ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 318.039ms"}}
[debug] [2025-07-29T07:54:46.522Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:46.522Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:46.523Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:46.523Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'NCYuVjrvLhPJP7obcTbx', {"user":"  prompt: 'NCYuVjrvLhPJP7obcTbx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'NCYuVjrvLhPJP7obcTbx',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextKeys: [ 'action', 'needsExpenseSelection', 'availableExpenses' ], {"user":"  contextKeys: [ 'action', 'needsExpenseSelection', 'availableExpenses' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [ 'action', 'needsExpenseSelection', 'availableExpenses' ],"}}
[info] >    contextAction: 'edit_expense', {"user":"  contextAction: 'edit_expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: 'edit_expense',"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing expense selection for editing... {"user":"Processing expense selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing expense selection for editing..."}}
[info] >  Selected expense ID for editing: NCYuVjrvLhPJP7obcTbx {"user":"Selected expense ID for editing: NCYuVjrvLhPJP7obcTbx","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected expense ID for editing: NCYuVjrvLhPJP7obcTbx"}}
[debug] [2025-07-29T07:54:59.221Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:59.221Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:59.221Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:59.221Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:59.221Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:59.223Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:59.223Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:59.224Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:59.225Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:54:59.227Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 3.0849ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 3.0849ms"}}
[debug] [2025-07-29T07:54:59.236Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:59.237Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:59.237Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:59.238Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:59.240Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:59.240Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:54:59.240Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:54:59.240Z"],"workRunningCount":1}
[debug] [2025-07-29T07:54:59.240Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:54:59.243Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:54:59.243Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:54:59.243Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:54:59.245Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  📨 Context action: edit_expense {"user":"📨 Context action: edit_expense","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: edit_expense"}}
[info] >  📨 Context action: { {"user":"📨 Context action: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: {"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   userId: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    selectedFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  selectedFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   selectedFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    currentFarmId: '7owH6eTfGSYqaRpgIsQE', {"user":"  currentFarmId: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   currentFarmId: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >    farms: [ {"user":"  farms: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farms: ["}}
[info] >      { {"user":"    {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     {"}}
[info] >        id: '7owH6eTfGSYqaRpgIsQE', {"user":"      id: '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       id: '7owH6eTfGSYqaRpgIsQE',"}}
[info] >        name: 'Haven_View', {"user":"      name: 'Haven_View',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       name: 'Haven_View',"}}
[info] >        location: [Object] {"user":"      location: [Object]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m       location: [Object]"}}
[info] >      } {"user":"    }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     }"}}
[info] >    ], {"user":"  ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ],"}}
[info] >    previousAnimalData: null, {"user":"  previousAnimalData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousAnimalData: null,"}}
[info] >    previousFarmData: null, {"user":"  previousFarmData: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   previousFarmData: null,"}}
[info] >    defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ', {"user":"  defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultFarmTypeId: 'B2bye8PZBQYqscxXoVqZ',"}}
[info] >    defaultStatusId: 'tEbttSFNlpr6gGm5y66l', {"user":"  defaultStatusId: 'tEbttSFNlpr6gGm5y66l',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultStatusId: 'tEbttSFNlpr6gGm5y66l',"}}
[info] >    defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw', {"user":"  defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   defaultSizeUnitId: 'ezVe7GdWq6SAxwaREyIw',"}}
[info] >    farmLocation: '', {"user":"  farmLocation: '',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   farmLocation: '',"}}
[info] >    context: { {"user":"  context: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   context: {"}}
[info] >      action: 'edit_expense', {"user":"    action: 'edit_expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     action: 'edit_expense',"}}
[info] >      needsExpenseSelection: true, {"user":"    needsExpenseSelection: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     needsExpenseSelection: true,"}}
[info] >      availableExpenses: [ [Object], [Object], [Object], [Object], [Object] ] {"user":"    availableExpenses: [ [Object], [Object], [Object], [Object], [Object] ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     availableExpenses: [ [Object], [Object], [Object], [Object], [Object] ]"}}
[info] >    }, {"user":"  },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   },"}}
[info] >    prompt: 'Change amount to 3000' {"user":"  prompt: 'Change amount to 3000'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Change amount to 3000'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-29T07:54:59.569Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 324.4046ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 324.4046ms"}}
[debug] [2025-07-29T07:54:59.570Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:54:59.571Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:54:59.571Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:54:59.571Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Change amount to 3000', {"user":"  prompt: 'Change amount to 3000',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Change amount to 3000',"}}
[info] >    hasContext: true, {"user":"  hasContext: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: true,"}}
[info] >    contextKeys: [ 'action', 'needsExpenseSelection', 'availableExpenses' ], {"user":"  contextKeys: [ 'action', 'needsExpenseSelection', 'availableExpenses' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [ 'action', 'needsExpenseSelection', 'availableExpenses' ],"}}
[info] >    contextAction: 'edit_expense', {"user":"  contextAction: 'edit_expense',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: 'edit_expense',"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Processing expense selection for editing... {"user":"Processing expense selection for editing...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing expense selection for editing..."}}
[info] >  Selected expense ID for editing: Change amount to 3000 {"user":"Selected expense ID for editing: Change amount to 3000","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Selected expense ID for editing: Change amount to 3000"}}
[debug] [2025-07-29T07:55:17.419Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:17.419Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:55:17.420Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:17.419Z"],"workRunningCount":1}
[debug] [2025-07-29T07:55:17.420Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:55:17.422Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:55:17.422Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:55:17.422Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:55:17.423Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:55:17.427Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 4.5605ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 4.5605ms"}}
[debug] [2025-07-29T07:55:17.429Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:55:17.429Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:55:17.430Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:55:17.430Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:55:17.431Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:17.431Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:55:17.431Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:17.431Z"],"workRunningCount":1}
[debug] [2025-07-29T07:55:17.431Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:55:17.433Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:55:17.434Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:55:17.434Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:55:17.434Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[warn] !  External network resource requested!
   - URL: "https://api.openai.com/v1/chat/completions"
 - Be careful, this may be a production service. {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"External network resource requested!\n   - URL: \"https://api.openai.com/v1/chat/completions\"\n - Be careful, this may be a production service."}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Analyze this image', {"user":"  prompt: 'Analyze this image',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Analyze this image',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Analyze this image', {"user":"  prompt: 'Analyze this image',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Analyze this image',"}}
[info] >    requestType: 'unknown', {"user":"  requestType: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'unknown',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Processing image analysis... {"user":"Processing image analysis...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing image analysis..."}}
[info] >  Calling OpenAI for animal data extraction... {"user":"Calling OpenAI for animal data extraction...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Calling OpenAI for animal data extraction..."}}
[info] >  Animal extraction response: ```json {"user":"Animal extraction response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Animal extraction response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "isAnimal": true, {"user":"  \"isAnimal\": true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"isAnimal\": true,"}}
[info] >    "animalData": { {"user":"  \"animalData\": {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"animalData\": {"}}
[info] >      "name": "", {"user":"    \"name\": \"\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"name\": \"\","}}
[info] >      "species": "cow", {"user":"    \"species\": \"cow\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"species\": \"cow\","}}
[info] >      "breed": "unknown", {"user":"    \"breed\": \"unknown\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"breed\": \"unknown\","}}
[info] >      "age": "adult", {"user":"    \"age\": \"adult\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"age\": \"adult\","}}
[info] >      "gender": "unknown", {"user":"    \"gender\": \"unknown\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"gender\": \"unknown\","}}
[info] >      "color": "brown with white markings", {"user":"    \"color\": \"brown with white markings\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"color\": \"brown with white markings\","}}
[info] >      "weight": "unknown", {"user":"    \"weight\": \"unknown\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"weight\": \"unknown\","}}
[info] >      "healthStatus": "healthy", {"user":"    \"healthStatus\": \"healthy\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"healthStatus\": \"healthy\","}}
[info] >      "description": "A brown cow with white markings on its face and body, standing in a grassy area." {"user":"    \"description\": \"A brown cow with white markings on its face and body, standing in a grassy area.\"","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     \"description\": \"A brown cow with white markings on its face and body, standing in a grassy area.\""}}
[info] >    } {"user":"  }","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   }"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  Error parsing animal data: SyntaxError: Unexpected token '`', "```json {"user":"Error parsing animal data: SyntaxError: Unexpected token '`', \"```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Error parsing animal data: SyntaxError: Unexpected token '`', \"```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >  "... is not valid JSON {"user":"\"... is not valid JSON","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m \"... is not valid JSON"}}
[info] >      at JSON.parse (<anonymous>) {"user":"    at JSON.parse (<anonymous>)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at JSON.parse (<anonymous>)"}}
[info] >      at exports.openAiChat (E:\python_projects_factory\animal\mcp-server\functions\controller\ai.js:3600:37) {"user":"    at exports.openAiChat (E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js:3600:37)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at exports.openAiChat (E:\\python_projects_factory\\animal\\mcp-server\\functions\\controller\\ai.js:3600:37)"}}
[info] >      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {"user":"    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"}}
[info] >  Processing as animal... {"user":"Processing as animal...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Processing as animal..."}}
[info] >  Calling OpenAI for animal analysis... {"user":"Calling OpenAI for animal analysis...","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Calling OpenAI for animal analysis..."}}
[info] >  Animal analysis response: ```json {"user":"Animal analysis response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Animal analysis response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "isAnimal": true, {"user":"  \"isAnimal\": true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"isAnimal\": true,"}}
[info] >    "species": "Cow", {"user":"  \"species\": \"Cow\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"species\": \"Cow\","}}
[info] >    "breed": "Unknown", {"user":"  \"breed\": \"Unknown\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"breed\": \"Unknown\","}}
[info] >    "gender": "unknown", {"user":"  \"gender\": \"unknown\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"gender\": \"unknown\","}}
[info] >    "estimatedAge": 3, {"user":"  \"estimatedAge\": 3,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"estimatedAge\": 3,"}}
[info] >    "estimatedWeight": 500, {"user":"  \"estimatedWeight\": 500,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"estimatedWeight\": 500,"}}
[info] >    "confidence": 95, {"user":"  \"confidence\": 95,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 95,"}}
[info] >    "healthStatus": "good", {"user":"  \"healthStatus\": \"good\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"healthStatus\": \"good\","}}
[info] >    "bodyCondition": "normal", {"user":"  \"bodyCondition\": \"normal\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"bodyCondition\": \"normal\","}}
[info] >    "visibleIssues": [], {"user":"  \"visibleIssues\": [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"visibleIssues\": [],"}}
[info] >    "colorMarkings": "brown with white markings on face and underbelly" {"user":"  \"colorMarkings\": \"brown with white markings on face and underbelly\"","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"colorMarkings\": \"brown with white markings on face and underbelly\""}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  Parsed animal data: { {"user":"Parsed animal data: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Parsed animal data: {"}}
[info] >    isAnimal: true, {"user":"  isAnimal: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   isAnimal: true,"}}
[info] >    species: 'Cow', {"user":"  species: 'Cow',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   species: 'Cow',"}}
[info] >    breed: 'Unknown', {"user":"  breed: 'Unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   breed: 'Unknown',"}}
[info] >    gender: 'unknown', {"user":"  gender: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   gender: 'unknown',"}}
[info] >    estimatedAge: 3, {"user":"  estimatedAge: 3,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   estimatedAge: 3,"}}
[info] >    estimatedWeight: 500, {"user":"  estimatedWeight: 500,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   estimatedWeight: 500,"}}
[info] >    confidence: 95, {"user":"  confidence: 95,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 95,"}}
[info] >    healthStatus: 'good', {"user":"  healthStatus: 'good',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   healthStatus: 'good',"}}
[info] >    bodyCondition: 'normal', {"user":"  bodyCondition: 'normal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   bodyCondition: 'normal',"}}
[info] >    visibleIssues: [], {"user":"  visibleIssues: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   visibleIssues: [],"}}
[info] >    colorMarkings: 'brown with white markings on face and underbelly' {"user":"  colorMarkings: 'brown with white markings on face and underbelly'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   colorMarkings: 'brown with white markings on face and underbelly'"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  AI Analysis Response: { {"user":"AI Analysis Response: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: {"}}
[info] >    "animalName": null, {"user":"  \"animalName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"animalName\": null,"}}
[info] >    "species": null, {"user":"  \"species\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"species\": null,"}}
[info] >    "breed": null, {"user":"  \"breed\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"breed\": null,"}}
[info] >    "age": null, {"user":"  \"age\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"age\": null,"}}
[info] >    "weight": null, {"user":"  \"weight\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"weight\": null,"}}
[info] >    "gender": null, {"user":"  \"gender\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"gender\": null,"}}
[info] >    "action": "analyze", {"user":"  \"action\": \"analyze\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"analyze\","}}
[info] >    "newName": null, {"user":"  \"newName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"newName\": null,"}}
[info] >    "confidence": 80 {"user":"  \"confidence\": 80","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 80"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[debug] [2025-07-29T07:55:32.135Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 14700.475699999999ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 14700.475699999999ms"}}
[debug] [2025-07-29T07:55:32.135Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:55:32.135Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:55:32.135Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:55:32.136Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[info] >  AI extracted info: { {"user":"AI extracted info: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI extracted info: {"}}
[info] >    animalName: null, {"user":"  animalName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   animalName: null,"}}
[info] >    species: null, {"user":"  species: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   species: null,"}}
[info] >    breed: null, {"user":"  breed: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   breed: null,"}}
[info] >    age: null, {"user":"  age: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   age: null,"}}
[info] >    weight: null, {"user":"  weight: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   weight: null,"}}
[info] >    gender: null, {"user":"  gender: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   gender: null,"}}
[info] >    action: 'analyze', {"user":"  action: 'analyze',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   action: 'analyze',"}}
[info] >    newName: null, {"user":"  newName: null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   newName: null,"}}
[info] >    confidence: 80 {"user":"  confidence: 80","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   confidence: 80"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  Using farm from dashboard selection: Haven_View {"user":"Using farm from dashboard selection: Haven_View","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Using farm from dashboard selection: Haven_View"}}
[debug] [2025-07-29T07:55:56.759Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:56.759Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:55:56.759Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:56.759Z"],"workRunningCount":1}
[debug] [2025-07-29T07:55:56.759Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:55:56.761Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:55:56.761Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:55:56.761Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:55:56.762Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:55:56.765Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 3.7112ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 3.7112ms"}}
[debug] [2025-07-29T07:55:56.768Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:55:56.769Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:55:56.769Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:55:56.769Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:55:56.770Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:56.770Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:55:56.770Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:55:56.770Z"],"workRunningCount":1}
[debug] [2025-07-29T07:55:56.770Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:55:56.772Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:55:56.772Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:55:56.773Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:55:56.773Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Save', {"user":"  prompt: 'Save',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Save',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  AI Analysis Response: {"action": "save", "newName": null, "confidence": 100} {"user":"AI Analysis Response: {\"action\": \"save\", \"newName\": null, \"confidence\": 100}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: {\"action\": \"save\", \"newName\": null, \"confidence\": 100}"}}
[info] >  Animal saved with ID: 8rwKB46Er6jU4p67MQR2 {"user":"Animal saved with ID: 8rwKB46Er6jU4p67MQR2","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Animal saved with ID: 8rwKB46Er6jU4p67MQR2"}}
[debug] [2025-07-29T07:56:01.047Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 4273.9661ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 4273.9661ms"}}
[debug] [2025-07-29T07:56:01.048Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:56:01.048Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:56:01.048Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:56:01.048Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:58:00.247Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:58:00.247Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:58:00.247Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:58:00.247Z"],"workRunningCount":1}
[debug] [2025-07-29T07:58:00.247Z] Accepted request OPTIONS /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:58:00.249Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:58:00.250Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:58:00.250Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:58:00.258Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[debug] [2025-07-29T07:58:00.260Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2.4582ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2.4582ms"}}
[debug] [2025-07-29T07:58:00.261Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:58:00.261Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:58:00.261Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:58:00.261Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:58:00.262Z] [work-queue] {"queuedWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:58:00.262Z"],"queueLength":1,"runningWork":[],"workRunningCount":0}
[debug] [2025-07-29T07:58:00.262Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":["/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat-2025-07-29T07:58:00.262Z"],"workRunningCount":1}
[debug] [2025-07-29T07:58:00.262Z] Accepted request POST /kissandost-9570f/australia-southeast1/animalApp/open-ai-chat --> australia-southeast1-animalApp
[debug] [2025-07-29T07:58:00.264Z] [functions] Runtime ready! Sending request! {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Runtime ready! Sending request!"}}
[debug] [2025-07-29T07:58:00.265Z] [functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat {"metadata":{"emulator":{"name":"functions"},"message":"[functions] Got req.url=/kissandost-9570f/australia-southeast1/animalApp/open-ai-chat, mapping to path=/open-ai-chat"}}
[debug] [2025-07-29T07:58:00.265Z] [worker-pool] submitRequest(triggerId=australia-southeast1-animalApp) {"metadata":{"emulator":{"name":"functions"},"message":"[worker-pool] submitRequest(triggerId=australia-southeast1-animalApp)"}}
[info] i  functions: Beginning execution of "australia-southeast1-animalApp" {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Beginning execution of \"australia-southeast1-animalApp\""}}
[debug] [2025-07-29T07:58:00.265Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: BUSY"}}
[info] >  ............................ { {"user":"............................ {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ............................ {"}}
[info] >    id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12', {"user":"  id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   id: 'obLA65ZT1JPo8Ytkrze6XAFBFE12',"}}
[info] >    email: '<EMAIL>', {"user":"  email: '<EMAIL>',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   email: '<EMAIL>',"}}
[info] >    role: 'owner', {"user":"  role: 'owner',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   role: 'owner',"}}
[info] >    language: 'en', {"user":"  language: 'en',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   language: 'en',"}}
[info] >    preferAudio: false, {"user":"  preferAudio: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   preferAudio: false,"}}
[info] >    offlineMode: false, {"user":"  offlineMode: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   offlineMode: false,"}}
[info] >    emailVerified: true, {"user":"  emailVerified: true,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   emailVerified: true,"}}
[info] >    createdAt: 1746969090959, {"user":"  createdAt: 1746969090959,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   createdAt: 1746969090959,"}}
[info] >    name: 'Dr. Javad Ahmed', {"user":"  name: 'Dr. Javad Ahmed',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   name: 'Dr. Javad Ahmed',"}}
[info] >    ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ], {"user":"  ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ownedFarms: [ 'ell4xZlOUxIwbSTdk4gp', 'YrwVdr1vr3KGqtXP3bqX' ],"}}
[info] >    profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90', {"user":"  profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   profilePicture: 'https://firebasestorage.googleapis.com/v0/b/kissandost-9570f.firebasestorage.app/o/profile-pictures%2FobLA65ZT1JPo8Ytkrze6XAFBFE12%2F1750768514392.jpg?alt=media&token=bb6acca1-9f21-4063-9db2-3b9994559e90',"}}
[info] >    lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 }, {"user":"  lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   lastLogin: Timestamp { _seconds: 1751376164, _nanoseconds: 17000000 },"}}
[info] >    updatedAt: *********7987, {"user":"  updatedAt: *********7987,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   updatedAt: *********7987,"}}
[info] >    assignedFarmIds: [ {"user":"  assignedFarmIds: [","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   assignedFarmIds: ["}}
[info] >      'ell4xZlOUxIwbSTdk4gp', {"user":"    'ell4xZlOUxIwbSTdk4gp',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'ell4xZlOUxIwbSTdk4gp',"}}
[info] >      'YrwVdr1vr3KGqtXP3bqX', {"user":"    'YrwVdr1vr3KGqtXP3bqX',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'YrwVdr1vr3KGqtXP3bqX',"}}
[info] >      '7owH6eTfGSYqaRpgIsQE', {"user":"    '7owH6eTfGSYqaRpgIsQE',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     '7owH6eTfGSYqaRpgIsQE',"}}
[info] >      'icSTOLwTaC6WYgWjsgDk', {"user":"    'icSTOLwTaC6WYgWjsgDk',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'icSTOLwTaC6WYgWjsgDk',"}}
[info] >      'BtniJTurDAn0saHHbEGF', {"user":"    'BtniJTurDAn0saHHbEGF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'BtniJTurDAn0saHHbEGF',"}}
[info] >      'wFSF3LbHQwPYy6A0pu94', {"user":"    'wFSF3LbHQwPYy6A0pu94',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'wFSF3LbHQwPYy6A0pu94',"}}
[info] >      'fNgkVoyHni01zmozumbK', {"user":"    'fNgkVoyHni01zmozumbK',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'fNgkVoyHni01zmozumbK',"}}
[info] >      'C6FceATDCJxVtji42Q3D', {"user":"    'C6FceATDCJxVtji42Q3D',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'C6FceATDCJxVtji42Q3D',"}}
[info] >      'M5Pahb6Vi1rdq7KZB5LF', {"user":"    'M5Pahb6Vi1rdq7KZB5LF',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'M5Pahb6Vi1rdq7KZB5LF',"}}
[info] >      'oXnGAP4V78vilhIVpIQN', {"user":"    'oXnGAP4V78vilhIVpIQN',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'oXnGAP4V78vilhIVpIQN',"}}
[info] >      'NpS3CdrpHvPZClMMDeFc', {"user":"    'NpS3CdrpHvPZClMMDeFc',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'NpS3CdrpHvPZClMMDeFc',"}}
[info] >      'TlS4g9PWKN1wNBtjHEoq', {"user":"    'TlS4g9PWKN1wNBtjHEoq',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'TlS4g9PWKN1wNBtjHEoq',"}}
[info] >      'rb2n32HuXJlFOk44cAkJ', {"user":"    'rb2n32HuXJlFOk44cAkJ',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'rb2n32HuXJlFOk44cAkJ',"}}
[info] >      'UWkNKuXggt70Oe4bkzcx', {"user":"    'UWkNKuXggt70Oe4bkzcx',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'UWkNKuXggt70Oe4bkzcx',"}}
[info] >      'JRAQxnj2u4vmHNuPHz3W', {"user":"    'JRAQxnj2u4vmHNuPHz3W',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JRAQxnj2u4vmHNuPHz3W',"}}
[info] >      'H3LThue9rQ8V1Pz8Qdal', {"user":"    'H3LThue9rQ8V1Pz8Qdal',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'H3LThue9rQ8V1Pz8Qdal',"}}
[info] >      'JI3IBGjWKZUnJxguyPTH' {"user":"    'JI3IBGjWKZUnJxguyPTH'","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m     'JI3IBGjWKZUnJxguyPTH'"}}
[info] >    ] {"user":"  ]","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   ]"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  🔍 REQUEST DEBUG: { {"user":"🔍 REQUEST DEBUG: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST DEBUG: {"}}
[info] >    prompt: 'Clear', {"user":"  prompt: 'Clear',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Clear',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextKeys: [], {"user":"  contextKeys: [],","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextKeys: [],"}}
[info] >    contextAction: undefined, {"user":"  contextAction: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined,"}}
[info] >    contextEditMode: undefined, {"user":"  contextEditMode: undefined,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextEditMode: undefined,"}}
[info] >    contextSelectedFarm: false {"user":"  contextSelectedFarm: false","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextSelectedFarm: false"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  AI Analysis Response: ```json {"user":"AI Analysis Response: ```json","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m AI Analysis Response: ```json"}}
[info] >  { {"user":"{","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m {"}}
[info] >    "action": "other", {"user":"  \"action\": \"other\",","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"action\": \"other\","}}
[info] >    "newName": null, {"user":"  \"newName\": null,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"newName\": null,"}}
[info] >    "confidence": 80 {"user":"  \"confidence\": 80","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   \"confidence\": 80"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  ``` {"user":"```","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m ```"}}
[info] >  🔍 REQUEST TYPE ANALYSIS: { {"user":"🔍 REQUEST TYPE ANALYSIS: {","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 🔍 REQUEST TYPE ANALYSIS: {"}}
[info] >    prompt: 'Clear', {"user":"  prompt: 'Clear',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   prompt: 'Clear',"}}
[info] >    requestType: 'unknown', {"user":"  requestType: 'unknown',","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   requestType: 'unknown',"}}
[info] >    hasContext: false, {"user":"  hasContext: false,","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   hasContext: false,"}}
[info] >    contextAction: undefined {"user":"  contextAction: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m   contextAction: undefined"}}
[info] >  } {"user":"}","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m }"}}
[info] >  📨 Context received: null {"user":"📨 Context received: null","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context received: null"}}
[info] >  📨 Context action: undefined {"user":"📨 Context action: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context action: undefined"}}
[info] >  📨 Context needsAnimalSelection: undefined {"user":"📨 Context needsAnimalSelection: undefined","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m 📨 Context needsAnimalSelection: undefined"}}
[info] >  Chat response: Is there anything specific you would like help with? Feel free to ask me any questions you may have. {"user":"Chat response: Is there anything specific you would like help with? Feel free to ask me any questions you may have.","metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"\u001b[90m> \u001b[39m Chat response: Is there anything specific you would like help with? Feel free to ask me any questions you may have."}}
[debug] [2025-07-29T07:58:03.011Z] Finishing up request with event=pause {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=pause"}}
[info] i  functions: Finished "australia-southeast1-animalApp" in 2745.8437ms {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finished \"australia-southeast1-animalApp\" in 2745.8437ms"}}
[debug] [2025-07-29T07:58:03.012Z] [worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"[worker-australia-southeast1-animalApp-2ecbaac3-bd83-4d85-abea-cf4de955896c]: IDLE"}}
[debug] [2025-07-29T07:58:03.012Z] Finishing up request with event=finish {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=finish"}}
[debug] [2025-07-29T07:58:03.012Z] Finishing up request with event=close {"metadata":{"emulator":{"name":"functions"},"function":{"name":"australia-southeast1-animalApp"},"extension":{},"message":"Finishing up request with event=close"}}
[debug] [2025-07-29T07:58:03.012Z] [work-queue] {"queuedWork":[],"queueLength":0,"runningWork":[],"workRunningCount":0}
